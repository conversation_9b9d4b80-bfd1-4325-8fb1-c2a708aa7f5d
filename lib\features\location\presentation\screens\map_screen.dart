import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/routes/app_router.dart';
import '../../services/adress_services.dart';

class MapScreen extends StatefulWidget {
  const MapScreen({super.key});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  final AddressService _addressService = AddressService();
  GoogleMapController? _mapController;
  LatLng? _selectedPosition;
  String _address = '';
  bool _isLoadingAddress = false;

  @override
  void initState() {
    super.initState();
    _initializeMap();
  }

  Future<void> _initializeMap() async {
    final permissionGranted = await _addressService.requestLocationPermission();
    if (!permissionGranted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Location permission not granted')),
      );
      return;
    }

    final position = await _addressService.getCurrentPosition();
    if (position != null) {
      setState(() {
        _selectedPosition = LatLng(position.latitude, position.longitude);
      });
      _getAddressFromLatLng(_selectedPosition!);
    }
  }

  void _onCameraMove(CameraPosition position) {
    _selectedPosition = position.target;
  }

  void _onCameraIdle() {
    if (_selectedPosition != null) {
      _getAddressFromLatLng(_selectedPosition!);
    }
  }

  Future<void> _getAddressFromLatLng(LatLng latLng) async {
    setState(() {
      _isLoadingAddress = true;
    });

    try {
      final placemarks = await _addressService.getAddressFromCoordinates(
        latLng.latitude,
        latLng.longitude,
      );
      if (placemarks != null && placemarks.isNotEmpty) {
        final p = placemarks.first;
        setState(() {
          _address = [
            p.street,
            p.locality,
            p.administrativeArea,
            p.postalCode,
            p.country
          ].where((part) => part != null && part.isNotEmpty).join(', ');
        });
      } else {
        setState(() {
          _address = 'Address not found';
        });
      }
    } catch (e) {
      setState(() {
        _address = 'Failed to fetch address';
      });
    } finally {
      setState(() {
        _isLoadingAddress = false;
      });
    }
  }

  void _confirmAddress() {
    if (_selectedPosition != null) {
      context.push(
        RouteNames.editAddress,
        extra: {
          'latitude': _selectedPosition!.latitude,
          'longitude': _selectedPosition!.longitude,
          'address': _address,
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Location'),
      ),
      body: _selectedPosition == null
          ? const Center(child: CircularProgressIndicator())
          : Stack(
              children: [
                GoogleMap(
                  initialCameraPosition: CameraPosition(
                    target: _selectedPosition!,
                    zoom: 16,
                  ),
                  onMapCreated: (controller) => _mapController = controller,
                  myLocationEnabled: true,
                  myLocationButtonEnabled: true,
                  onCameraMove: _onCameraMove,
                  onCameraIdle: _onCameraIdle,
                ),

                // Center marker icon
                const Center(
                  child: Icon(Icons.location_on, size: 48, color: Colors.red),
                ),

                // Bottom Address Container
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black26,
                          blurRadius: 8,
                          offset: Offset(0, -2),
                        ),
                      ],
                      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                    ),
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _isLoadingAddress
                            ? const CircularProgressIndicator()
                            : Text(
                                _address,
                                textAlign: TextAlign.center,
                                style: const TextStyle(fontSize: 16),
                              ),
                        const SizedBox(height: 12),
                        ElevatedButton(
                          onPressed: _confirmAddress,
                          style: ElevatedButton.styleFrom(
                            minimumSize: const Size.fromHeight(48),
                          ),
                          child: const Text('Confirm Location'),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
