import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/routes/app_router.dart';
import '../../../../data/models/adress_model.dart';
import '../../services/adress_services.dart';

class MapScreen extends StatefulWidget {
  final bool returnCoordinates;
  final AddressModel? existingAddress;

  const MapScreen({
    super.key,
    this.returnCoordinates = true,
    this.existingAddress,
  });

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  final AddressService _addressService = AddressService();
  GoogleMapController? _mapController;
  LatLng? _selectedPosition;
  String _address = '';
  bool _isLoadingAddress = false;
  bool _isLoadingLocation = false;

  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _initializeMap();
  }

  Future<void> _initializeMap() async {
    final permissionGranted = await _addressService.requestLocationPermission();
    if (!permissionGranted) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Location permission not granted')),
        );
      }
      return;
    }

    if (widget.existingAddress != null &&
        widget.existingAddress!.latitude != null &&
        widget.existingAddress!.longitude != null) {
      setState(() {
        _selectedPosition = LatLng(
          widget.existingAddress!.latitude!.toDouble(),
          widget.existingAddress!.longitude!.toDouble(),
        );
        _address = widget.existingAddress!.fullAddress ?? '';
      });
      return;
    }

    final position = await _addressService.getCurrentPosition();
    if (position != null) {
      setState(() {
        _selectedPosition = LatLng(position.latitude, position.longitude);
      });
      _getAddressFromLatLng(_selectedPosition!);
    }
  }

  void _onCameraMove(CameraPosition position) {
    _selectedPosition = position.target;
  }

  void _onCameraIdle() {
    if (_selectedPosition != null) {
      _getAddressFromLatLng(_selectedPosition!);
    }
  }

  Future<void> _getAddressFromLatLng(LatLng latLng) async {
    setState(() {
      _isLoadingAddress = true;
    });

    try {
      final placemarks = await _addressService.getAddressFromCoordinates(
        latLng.latitude,
        latLng.longitude,
      );
      if (placemarks != null && placemarks.isNotEmpty) {
        final p = placemarks.first;
        setState(() {
          _address = [
            p.street,
            p.locality,
            p.administrativeArea,
            p.postalCode,
            p.country
          ].where((part) => part != null && part.isNotEmpty).join(', ');
        });
      } else {
        setState(() {
          _address = 'Address not found';
        });
      }
    } catch (e) {
      setState(() {
        _address = 'Failed to fetch address';
      });
    } finally {
      setState(() {
        _isLoadingAddress = false;
      });
    }
  }

  Future<void> _searchAddress(String query) async {
    if (query.isEmpty) return;

    setState(() {
      _isLoadingLocation = true;
    });

    try {
      final locations = await _addressService.searchAddresses(query);

      if (locations != null && locations.isNotEmpty) {
        final location = locations.first;
        final placemarks = await _addressService.getPlacemarkFromLocation(location);

        if (placemarks != null && placemarks.isNotEmpty) {
          final placemark = placemarks.first;

          final latLng = LatLng(location.latitude, location.longitude);
          setState(() {
            _selectedPosition = latLng;
            _address = [
              placemark.street,
              placemark.locality,
              placemark.administrativeArea,
              placemark.postalCode,
              placemark.country
            ].where((part) => part != null && part.isNotEmpty).join(', ');
          });

          _mapController?.animateCamera(CameraUpdate.newLatLngZoom(latLng, 16));
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('No results found for "$query"')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to search address')),
      );
    } finally {
      setState(() {
        _isLoadingLocation = false;
      });
    }
  }

  void _confirmAddress() {
    if (_selectedPosition != null) {
      if (widget.returnCoordinates) {
        // Navigate directly to home (or your desired screen) instead of popping back to map
        context.pop({
          'latitude': _selectedPosition!.latitude,
          'longitude': _selectedPosition!.longitude,
          'address': _address,
          }); 
      } else {
        if (widget.existingAddress != null) {
          final updatedAddress = widget.existingAddress!.copyWith(
            latitude: _selectedPosition!.latitude,
            longitude: _selectedPosition!.longitude,
          );
          context.push(RouteNames.editAddress, extra: updatedAddress);
        } else {
          context.push(
            RouteNames.editAddress,
            extra: {
              'latitude': _selectedPosition!.latitude,
              'longitude': _selectedPosition!.longitude,
              'address': _address,
            },
          );
        }
      }
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Location'),
      ),
      body: _selectedPosition == null
          ? const Center(child: CircularProgressIndicator())
          : Stack(
              children: [
                GoogleMap(
                  initialCameraPosition: CameraPosition(
                    target: _selectedPosition!,
                    zoom: 16,
                  ),
                  onMapCreated: (controller) => _mapController = controller,
                  myLocationEnabled: true,
                  myLocationButtonEnabled: true,
                  onCameraMove: _onCameraMove,
                  onCameraIdle: _onCameraIdle,
                ),

                const Center(
                  child: Icon(Icons.location_on, size: 48, color: Colors.red),
                ),

                // Search Bar at Top
                Positioned(
                  top: 16,
                  left: 16,
                  right: 16,
                  child: Material(
                    elevation: 6,
                    borderRadius: BorderRadius.circular(32),
                    shadowColor: Colors.black26,
                    child: TextField(
                      controller: _searchController,
                      focusNode: _searchFocusNode,
                      textInputAction: TextInputAction.search,
                      onSubmitted: _searchAddress,
                      decoration: InputDecoration(
                        hintText: 'Search for an address',
                        prefixIcon: const Icon(Icons.search),
                        filled: true,
                        fillColor: Colors.white,
                        contentPadding: const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(32),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(32),
                          borderSide: BorderSide.none,
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(32),
                          borderSide: BorderSide(color: Colors.blue, width: 1.5),
                        ),
                      ),
                    ),
                  ),
                ),

                // Bottom Address + Button
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black26,
                          blurRadius: 8,
                          offset: Offset(0, -2),
                        ),
                      ],
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(16)),
                    ),
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _isLoadingAddress || _isLoadingLocation
                            ? const CircularProgressIndicator()
                            : Text(
                                _address,
                                textAlign: TextAlign.center,
                                style: const TextStyle(fontSize: 16),
                              ),
                        const SizedBox(height: 12),
                        ElevatedButton(
                          onPressed: _confirmAddress,
                          style: ElevatedButton.styleFrom(
                            minimumSize: const Size.fromHeight(48),
                          ),
                          child: const Text('Confirm Location'),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
